import 'package:flutter/material.dart';

import '../config/app_theme.dart';
import '../models/content_item.dart';
import '../services/content_service.dart';
import '../services/theme_manager.dart';
import 'content_detail_page.dart';
import 'text_card_detail_page.dart';
import 'widgets/hero_section.dart';
import 'widgets/quick_actions_bar.dart';
import 'widgets/premium_content_card.dart';

/// 高级内容库首页 - 美观大气的重新设计
class PremiumContentHomePage extends StatefulWidget {
  const PremiumContentHomePage({super.key});

  @override
  State<PremiumContentHomePage> createState() => _PremiumContentHomePageState();
}

class _PremiumContentHomePageState extends State<PremiumContentHomePage>
    with TickerProviderStateMixin {
  final ContentService _contentService = ContentService();
  List<ContentItem> _allItems = [];
  List<ContentItem> _filteredItems = [];
  bool _isLoading = true;
  String _searchQuery = '';
  ContentType? _selectedCategory;
  bool _showFavoritesOnly = false;

  // 动画控制器
  late AnimationController _heroAnimationController;
  late AnimationController _cardAnimationController;
  late AnimationController _floatingAnimationController;

  // 动画
  late Animation<double> _heroFadeAnimation;
  late Animation<Offset> _heroSlideAnimation;

  // 批量管理相关状态
  bool _isSelectionMode = false;
  final Set<String> _selectedItems = <String>{};

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeContent();
  }

  void _initializeAnimations() {
    // Hero区域动画
    _heroAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // 卡片动画
    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 浮动元素动画
    _floatingAnimationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    // 创建动画
    _heroFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _heroAnimationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _heroSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _heroAnimationController,
        curve: const Interval(0.2, 0.8, curve: Curves.easeOutCubic),
      ),
    );

    // 启动动画
    _heroAnimationController.forward();
  }

  Future<void> _initializeContent() async {
    setState(() {
      _isLoading = true;
    });

    await _contentService.initialize();
    _loadContent();

    setState(() {
      _isLoading = false;
    });

    // 启动卡片动画
    _cardAnimationController.forward();
  }

  void _loadContent() {
    _allItems = _contentService.getAllItems();
    _filterItems();
  }

  void _filterItems() {
    List<ContentItem> items = _allItems;

    // 按类型筛选
    if (_selectedCategory != null) {
      items =
          items
              .where((item) => _getItemCategory(item) == _selectedCategory)
              .toList();
    }

    // 按收藏筛选
    if (_showFavoritesOnly) {
      items = items.where((item) => item.isFavorite).toList();
    }

    // 按搜索关键词筛选
    if (_searchQuery.isNotEmpty) {
      items =
          items.where((item) {
            return item.title.toLowerCase().contains(
                  _searchQuery.toLowerCase(),
                ) ||
                item.tags.any(
                  (tag) =>
                      tag.toLowerCase().contains(_searchQuery.toLowerCase()),
                );
          }).toList();
    }

    setState(() {
      _filteredItems = items;
    });
  }

  /// 获取内容项的分类
  ContentType _getItemCategory(ContentItem item) {
    switch (item.type) {
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return ContentType.textCard;
      case ContentType.image:
      case ContentType.svg:
        return ContentType.image;
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        return ContentType.markdown;
      default:
        return item.type;
    }
  }

  /// 检查是否是Markdown分块模式
  bool _isMarkdownBlockMode(ContentItem item) {
    return item.type == ContentType.markdownBlocks;
  }

  /// 检查是否是文本卡片集合
  bool _isTextCardCollection(ContentItem item) {
    return item.type == ContentType.textCardCollection;
  }

  /// 检查是否是文本卡片
  bool _isTextCard(ContentItem item) {
    if (item.tags.contains('text_card')) {
      return true;
    }
    if (item.type == ContentType.textCard) {
      return true;
    }
    return false;
  }

  void _viewContentItem(ContentItem item) {
    if (_isTextCard(item) || _isTextCardCollection(item)) {
      _openTextCardDetail(item);
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => ContentDetailPage(item: item)),
      ).then((_) => _refreshContent());
    }
  }

  void _openTextCardDetail(ContentItem item) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => TextCardDetailPage(item: item)),
    ).then((_) => _refreshContent());
  }

  Future<void> _refreshContent() async {
    await _initializeContent();
  }

  @override
  void dispose() {
    _heroAnimationController.dispose();
    _cardAnimationController.dispose();
    _floatingAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(
          parent: AlwaysScrollableScrollPhysics(),
        ),
        slivers: [
          // Hero区域
          _buildHeroSection(),

          // 快速操作栏
          _buildQuickActionsSection(),

          // 内容展示区域
          if (_isLoading)
            _buildLoadingSection()
          else if (_filteredItems.isEmpty)
            _buildEmptySection()
          else
            _buildContentSections(),

          // 底部安全区域
          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// 构建Hero区域
  Widget _buildHeroSection() {
    return SliverToBoxAdapter(
      child: AnimatedBuilder(
        animation: _heroAnimationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _heroFadeAnimation,
            child: SlideTransition(
              position: _heroSlideAnimation,
              child: HeroSection(
                searchQuery: _searchQuery,
                onSearchChanged: (query) {
                  setState(() {
                    _searchQuery = query;
                  });
                  _filterItems();
                },
                showFavoritesOnly: _showFavoritesOnly,
                onFavoritesToggle: () {
                  setState(() {
                    _showFavoritesOnly = !_showFavoritesOnly;
                  });
                  _filterItems();
                },
                totalItems: _allItems.length,
                floatingAnimationController: _floatingAnimationController,
                onBackPressed: () => Navigator.of(context).pop(),
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建快速操作区域
  Widget _buildQuickActionsSection() {
    return SliverToBoxAdapter(
      child: QuickActionsBar(
        onActionTapped: (action) {
          // 处理快速操作
          Navigator.of(context).pop();
        },
      ),
    );
  }

  /// 构建加载区域
  Widget _buildLoadingSection() {
    return const SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
            ),
            SizedBox(height: 16),
            Text(
              '正在加载内容...',
              style: TextStyle(color: AppTheme.textMediumColor, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建空状态区域
  Widget _buildEmptySection() {
    return SliverFillRemaining(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryColor.withValues(alpha: 0.1),
                    AppTheme.primaryColor.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(60),
              ),
              child: const Icon(
                Icons.inventory_2_outlined,
                size: 48,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              '还没有保存任何内容',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: AppTheme.textDarkColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '开始创建您的第一个内容吧',
              style: TextStyle(fontSize: 14, color: AppTheme.textMediumColor),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContentSections() {
    // 按内容类型分组
    final Map<ContentType, List<ContentItem>> groupedItems = {};
    for (final item in _filteredItems) {
      final category = _getItemCategory(item);
      groupedItems.putIfAbsent(category, () => []).add(item);
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final entries = groupedItems.entries.toList();
        if (index >= entries.length) return null;

        final entry = entries[index];
        return _buildContentSection(entry.key, entry.value);
      }, childCount: groupedItems.length),
    );
  }

  /// 构建单个内容分组
  Widget _buildContentSection(ContentType type, List<ContentItem> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分组标题
        Padding(
          padding: const EdgeInsets.fromLTRB(20, 32, 20, 16),
          child: Row(
            children: [
              Container(
                width: 4,
                height: 20,
                decoration: BoxDecoration(
                  color: ThemeManager().getContentTypeColor(
                    type.toString().split('.').last,
                  ),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                _getContentTypeName(type),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: AppTheme.textDarkColor,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: ThemeManager()
                      .getContentTypeColor(type.toString().split('.').last)
                      .withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${items.length}',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: ThemeManager().getContentTypeColor(
                      type.toString().split('.').last,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // 内容网格
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: _buildContentGrid(items),
        ),
      ],
    );
  }

  /// 构建内容网格
  Widget _buildContentGrid(List<ContentItem> items) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16,
        mainAxisSpacing: 20,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return TweenAnimationBuilder<double>(
          duration: Duration(milliseconds: 300 + (index * 100)),
          tween: Tween(begin: 0.0, end: 1.0),
          builder: (context, value, child) {
            return Transform.translate(
              offset: Offset(0, 20 * (1 - value)),
              child: Opacity(
                opacity: value,
                child: PremiumContentCard(
                  item: item,
                  onTap: () => _viewContentItem(item),
                  onLongPress: () => _enterSelectionMode(item.id),
                  isSelected: _selectedItems.contains(item.id),
                  isSelectionMode: _isSelectionMode,
                  isMarkdownBlockMode: _isMarkdownBlockMode(item),
                  isTextCardCollection: _isTextCardCollection(item),
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// 获取内容类型的显示名称
  String _getContentTypeName(ContentType type) {
    switch (type) {
      case ContentType.markdown:
        return 'Markdown文档';
      case ContentType.textCard:
        return '文本卡片';
      case ContentType.image:
        return '图片资源';
      case ContentType.html:
        return 'HTML文档';
      case ContentType.pdf:
        return 'PDF文档';
      case ContentType.voice:
        return '语音文件';
      default:
        return '其他文件';
    }
  }

  // 选择模式相关方法
  void _enterSelectionMode(String itemId) {
    setState(() {
      _isSelectionMode = true;
      _selectedItems.clear();
      _selectedItems.add(itemId);
    });
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.primaryColor, AppTheme.primaryLightColor],
        ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.4),
            offset: const Offset(0, 8),
            blurRadius: 24,
            spreadRadius: 0,
          ),
        ],
      ),
      child: FloatingActionButton(
        heroTag: 'premium_content_home_page_fab',
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: const Icon(Icons.add, color: Colors.white, size: 28),
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }
}
