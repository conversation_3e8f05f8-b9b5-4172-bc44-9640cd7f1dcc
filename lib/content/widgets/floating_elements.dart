import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 浮动动画元素组件 - 为Hero区域添加动态背景
class FloatingElements extends StatelessWidget {
  final AnimationController animationController;

  const FloatingElements({
    super.key,
    required this.animationController,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animationController,
      builder: (context, child) {
        return Stack(
          children: [
            // 大圆形元素1
            _buildFloatingElement(
              size: 120,
              color: Colors.white.withValues(alpha: 0.1),
              offsetX: 0.8,
              offsetY: 0.2,
              rotationSpeed: 1.0,
              translateSpeed: 0.5,
            ),

            // 中圆形元素2
            _buildFloatingElement(
              size: 80,
              color: Colors.white.withValues(alpha: 0.08),
              offsetX: 0.1,
              offsetY: 0.6,
              rotationSpeed: -0.8,
              translateSpeed: 0.3,
            ),

            // 小圆形元素3
            _buildFloatingElement(
              size: 60,
              color: Colors.white.withValues(alpha: 0.12),
              offsetX: 0.7,
              offsetY: 0.8,
              rotationSpeed: 1.2,
              translateSpeed: 0.7,
            ),

            // 三角形元素1
            _buildFloatingTriangle(
              size: 40,
              color: Colors.white.withValues(alpha: 0.06),
              offsetX: 0.2,
              offsetY: 0.3,
              rotationSpeed: -1.5,
              translateSpeed: 0.4,
            ),

            // 三角形元素2
            _buildFloatingTriangle(
              size: 30,
              color: Colors.white.withValues(alpha: 0.08),
              offsetX: 0.9,
              offsetY: 0.5,
              rotationSpeed: 2.0,
              translateSpeed: 0.6,
            ),

            // 方形元素1
            _buildFloatingSquare(
              size: 25,
              color: Colors.white.withValues(alpha: 0.05),
              offsetX: 0.05,
              offsetY: 0.1,
              rotationSpeed: 1.8,
              translateSpeed: 0.2,
            ),

            // 方形元素2
            _buildFloatingSquare(
              size: 35,
              color: Colors.white.withValues(alpha: 0.07),
              offsetX: 0.85,
              offsetY: 0.9,
              rotationSpeed: -1.3,
              translateSpeed: 0.8,
            ),

            // 线条元素
            _buildFloatingLine(
              length: 60,
              color: Colors.white.withValues(alpha: 0.04),
              offsetX: 0.4,
              offsetY: 0.15,
              rotationSpeed: 0.5,
              translateSpeed: 0.3,
            ),
          ],
        );
      },
    );
  }

  /// 构建浮动圆形元素
  Widget _buildFloatingElement({
    required double size,
    required Color color,
    required double offsetX,
    required double offsetY,
    required double rotationSpeed,
    required double translateSpeed,
  }) {
    final animation = animationController.value;
    final rotationAngle = animation * 2 * math.pi * rotationSpeed;
    final translateOffset = math.sin(animation * 2 * math.pi * translateSpeed) * 10;

    return Positioned(
      left: offsetX * 300 + translateOffset,
      top: offsetY * 280 + translateOffset * 0.5,
      child: Transform.rotate(
        angle: rotationAngle,
        child: Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建浮动三角形元素
  Widget _buildFloatingTriangle({
    required double size,
    required Color color,
    required double offsetX,
    required double offsetY,
    required double rotationSpeed,
    required double translateSpeed,
  }) {
    final animation = animationController.value;
    final rotationAngle = animation * 2 * math.pi * rotationSpeed;
    final translateOffset = math.cos(animation * 2 * math.pi * translateSpeed) * 8;

    return Positioned(
      left: offsetX * 300 + translateOffset,
      top: offsetY * 280 + translateOffset * 0.3,
      child: Transform.rotate(
        angle: rotationAngle,
        child: CustomPaint(
          size: Size(size, size),
          painter: TrianglePainter(color: color),
        ),
      ),
    );
  }

  /// 构建浮动方形元素
  Widget _buildFloatingSquare({
    required double size,
    required Color color,
    required double offsetX,
    required double offsetY,
    required double rotationSpeed,
    required double translateSpeed,
  }) {
    final animation = animationController.value;
    final rotationAngle = animation * 2 * math.pi * rotationSpeed;
    final translateOffset = math.sin(animation * 2 * math.pi * translateSpeed + math.pi / 4) * 6;

    return Positioned(
      left: offsetX * 300 + translateOffset,
      top: offsetY * 280 + translateOffset * 0.7,
      child: Transform.rotate(
        angle: rotationAngle,
        child: Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(size * 0.2),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.08),
              width: 1,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建浮动线条元素
  Widget _buildFloatingLine({
    required double length,
    required Color color,
    required double offsetX,
    required double offsetY,
    required double rotationSpeed,
    required double translateSpeed,
  }) {
    final animation = animationController.value;
    final rotationAngle = animation * 2 * math.pi * rotationSpeed;
    final translateOffset = math.sin(animation * 2 * math.pi * translateSpeed + math.pi / 3) * 12;

    return Positioned(
      left: offsetX * 300 + translateOffset,
      top: offsetY * 280 + translateOffset * 0.2,
      child: Transform.rotate(
        angle: rotationAngle,
        child: Container(
          width: length,
          height: 2,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(1),
          ),
        ),
      ),
    );
  }
}

/// 三角形绘制器
class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size.width / 2, 0);
    path.lineTo(0, size.height);
    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);

    // 绘制边框
    final borderPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    canvas.drawPath(path, borderPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
