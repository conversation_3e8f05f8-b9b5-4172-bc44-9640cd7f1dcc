import 'package:flutter/material.dart';
import '../../config/app_theme.dart';

/// 快速操作栏组件
class QuickActionsBar extends StatelessWidget {
  final Function(String) onActionTapped;

  const QuickActionsBar({
    super.key,
    required this.onActionTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          const Text(
            '快速创建',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: AppTheme.textDarkColor,
            ),
          ),

          const SizedBox(height: 16),

          // 操作按钮列表
          SizedBox(
            height: 80,
            child: ListView(
              scrollDirection: Axis.horizontal,
              physics: const BouncingScrollPhysics(),
              children: [
                _buildActionButton(
                  icon: Icons.text_fields,
                  label: 'Markdown',
                  gradient: AppTheme.blueGradient,
                  onTap: () => onActionTapped('markdown'),
                ),
                const SizedBox(width: 16),
                _buildActionButton(
                  icon: Icons.credit_card,
                  label: '文本卡片',
                  gradient: AppTheme.purpleGradient,
                  onTap: () => onActionTapped('text_card'),
                ),
                const SizedBox(width: 16),
                _buildActionButton(
                  icon: Icons.image,
                  label: '图片',
                  gradient: AppTheme.orangeGradient,
                  onTap: () => onActionTapped('image'),
                ),
                const SizedBox(width: 16),
                _buildActionButton(
                  icon: Icons.code,
                  label: 'HTML',
                  gradient: AppTheme.greenGradient,
                  onTap: () => onActionTapped('html'),
                ),
                const SizedBox(width: 16),
                _buildActionButton(
                  icon: Icons.picture_as_pdf,
                  label: 'PDF',
                  gradient: AppTheme.redGradient,
                  onTap: () => onActionTapped('pdf'),
                ),
                const SizedBox(width: 16),
                _buildActionButton(
                  icon: Icons.mic,
                  label: '语音',
                  gradient: AppTheme.chineseGradient,
                  onTap: () => onActionTapped('voice'),
                ),
                const SizedBox(width: 16),
                _buildActionButton(
                  icon: Icons.folder_open,
                  label: '导入文件',
                  gradient: const LinearGradient(
                    colors: [Color(0xFF6B7280), Color(0xFF4B5563)],
                  ),
                  onTap: () => onActionTapped('import'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required LinearGradient gradient,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: gradient.colors.first.withValues(alpha: 0.3),
              offset: const Offset(0, 4),
              blurRadius: 12,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(20),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 图标
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // 标签
                  Text(
                    label,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
