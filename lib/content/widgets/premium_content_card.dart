import 'package:flutter/material.dart';

import '../../config/app_theme.dart';
import '../../models/content_item.dart';
import '../../services/theme_manager.dart';

/// 高级内容卡片组件 - 美观的内容展示卡片
class PremiumContentCard extends StatefulWidget {
  final ContentItem item;
  final VoidCallback onTap;
  final VoidCallback onLongPress;
  final bool isSelected;
  final bool isSelectionMode;
  final bool isMarkdownBlockMode;
  final bool isTextCardCollection;

  const PremiumContentCard({
    super.key,
    required this.item,
    required this.onTap,
    required this.onLongPress,
    required this.isSelected,
    required this.isSelectionMode,
    required this.isMarkdownBlockMode,
    required this.isTextCardCollection,
  });

  @override
  State<PremiumContentCard> createState() => _PremiumContentCardState();
}

class _PremiumContentCardState extends State<PremiumContentCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(parent: _hoverController, curve: Curves.easeOut));

    _elevationAnimation = Tween<double>(
      begin: 0.0,
      end: 8.0,
    ).animate(CurvedAnimation(parent: _hoverController, curve: Curves.easeOut));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _hoverController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(
                    alpha: 0.08 + (_elevationAnimation.value * 0.02),
                  ),
                  offset: Offset(0, 4 + _elevationAnimation.value),
                  blurRadius: 16 + _elevationAnimation.value * 2,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(24),
              child: InkWell(
                onTap: widget.onTap,
                onLongPress: widget.onLongPress,
                onTapDown: (_) => _hoverController.forward(),
                onTapUp: (_) => _hoverController.reverse(),
                onTapCancel: () => _hoverController.reverse(),
                borderRadius: BorderRadius.circular(24),
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(24),
                    border:
                        widget.isSelectionMode && widget.isSelected
                            ? Border.all(color: AppTheme.primaryColor, width: 2)
                            : null,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 内容预览区域
                      _buildPreviewSection(),

                      // 内容信息区域
                      Expanded(child: _buildInfoSection()),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建预览区域
  Widget _buildPreviewSection() {
    return Container(
      height: 140,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        gradient: _getContentGradient(),
      ),
      child: Stack(
        children: [
          // 背景图案
          Positioned.fill(child: _buildBackgroundPattern()),

          // 主要内容图标
          Center(child: _buildMainIcon()),

          // 特殊标识
          if (widget.isMarkdownBlockMode || widget.isTextCardCollection)
            Positioned(top: 12, right: 12, child: _buildSpecialBadge()),

          // 选择复选框
          if (widget.isSelectionMode)
            Positioned(top: 12, left: 12, child: _buildSelectionCheckbox()),

          // 收藏图标
          if (widget.item.isFavorite && !widget.isSelectionMode)
            Positioned(top: 12, right: 12, child: _buildFavoriteIcon()),
        ],
      ),
    );
  }

  /// 构建背景图案
  Widget _buildBackgroundPattern() {
    return CustomPaint(
      painter: BackgroundPatternPainter(
        color: Colors.white.withValues(alpha: 0.1),
      ),
      child: Container(),
    );
  }

  /// 构建主要图标
  Widget _buildMainIcon() {
    return Container(
      width: 56,
      height: 56,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(28),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Icon(_getContentIcon(), color: Colors.white, size: 28),
    );
  }

  /// 构建特殊标识
  Widget _buildSpecialBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.25),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        widget.isMarkdownBlockMode ? '分块' : '集合',
        style: const TextStyle(
          fontSize: 10,
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// 构建选择复选框
  Widget _buildSelectionCheckbox() {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: widget.isSelected ? AppTheme.primaryColor : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              widget.isSelected
                  ? AppTheme.primaryColor
                  : Colors.white.withValues(alpha: 0.7),
          width: 2,
        ),
      ),
      child:
          widget.isSelected
              ? const Icon(Icons.check, color: Colors.white, size: 16)
              : null,
    );
  }

  /// 构建收藏图标
  Widget _buildFavoriteIcon() {
    return Container(
      width: 28,
      height: 28,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(14),
      ),
      child: const Icon(Icons.favorite, color: Colors.white, size: 16),
    );
  }

  /// 构建信息区域
  Widget _buildInfoSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Text(
            widget.item.title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textDarkColor,
              height: 1.2,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 8),

          // 类型标签
          _buildTypeChip(),

          const Spacer(),

          // 底部信息
          Row(
            children: [
              Icon(Icons.access_time, size: 12, color: Colors.grey.shade500),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  _formatDate(widget.item.createdAt),
                  style: TextStyle(fontSize: 11, color: Colors.grey.shade500),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建类型标签
  Widget _buildTypeChip() {
    final color = ThemeManager().getContentTypeColor(
      widget.item.type.toString().split('.').last,
    );

    String displayName = _getContentTypeName();
    if (widget.isMarkdownBlockMode) {
      displayName = 'Markdown(分块)';
    } else if (widget.isTextCardCollection) {
      displayName = '卡片集合';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Text(
        displayName,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }

  /// 获取内容渐变色
  LinearGradient _getContentGradient() {
    return ThemeManager().getContentTypeGradient(
      widget.item.type.toString().split('.').last,
    );
  }

  /// 获取内容图标
  IconData _getContentIcon() {
    switch (widget.item.type) {
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return Icons.credit_card;
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        return Icons.description;
      case ContentType.image:
      case ContentType.svg:
        return Icons.image;
      case ContentType.html:
        return Icons.code;
      case ContentType.pdf:
        return Icons.picture_as_pdf;
      case ContentType.voice:
        return Icons.mic;
    }
  }

  /// 获取内容类型名称
  String _getContentTypeName() {
    switch (widget.item.type) {
      case ContentType.textCard:
        return '文本卡片';
      case ContentType.textCardCollection:
        return '卡片集合';
      case ContentType.markdown:
        return 'Markdown';
      case ContentType.markdownBlocks:
        return 'Markdown';
      case ContentType.image:
        return '图片';
      case ContentType.svg:
        return 'SVG';
      case ContentType.html:
        return 'HTML';
      case ContentType.pdf:
        return 'PDF';
      case ContentType.voice:
        return '语音';
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}

/// 背景图案绘制器
class BackgroundPatternPainter extends CustomPainter {
  final Color color;

  BackgroundPatternPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    // 绘制一些装饰性的几何图形
    for (int i = 0; i < 8; i++) {
      final x = (i * 30.0) % size.width;
      final y = (i * 20.0) % size.height;
      canvas.drawCircle(Offset(x, y), 2, paint);
    }

    // 绘制一些线条
    final linePaint =
        Paint()
          ..color = color
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1;

    for (int i = 0; i < 3; i++) {
      final startX = (i * 50.0) % size.width;
      final startY = (i * 40.0) % size.height;
      final endX = startX + 20;
      final endY = startY + 10;
      canvas.drawLine(Offset(startX, startY), Offset(endX, endY), linePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
