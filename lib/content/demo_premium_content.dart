import 'package:flutter/material.dart';
import '../models/content_item.dart';
import 'premium_content_home_page.dart';

/// 演示高级内容库首页的示例页面
class DemoPremiumContent extends StatelessWidget {
  const DemoPremiumContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('内容库首页 - 高级设计'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: const PremiumContentHomePage(),
    );
  }

  /// 创建演示数据
  static List<ContentItem> createDemoData() {
    return [
      // Markdown文档
      ContentItem(
        title: '项目开发文档',
        type: ContentType.markdown,
        content: '# 项目开发文档\n\n这是一个示例Markdown文档...',
        tags: ['开发', '文档'],
        isFavorite: true,
      ),

      // Markdown分块文档
      ContentItem(
        title: '学习笔记合集',
        type: ContentType.markdownBlocks,
        content: '分块内容示例...',
        tags: ['学习', '笔记'],
      ),

      // 文本卡片
      ContentItem(
        title: '每日金句',
        type: ContentType.textCard,
        content: '{"text": "生活不是等待暴风雨过去，而是学会在雨中跳舞。"}',
        tags: ['text_card', '金句'],
        isFavorite: true,
      ),

      // 文本卡片集合
      ContentItem(
        title: '励志语录集',
        type: ContentType.textCardCollection,
        content: '{"cards": [{"text": "成功的秘诀在于坚持自己的目标。"}]}',
        tags: ['text_card', '励志'],
      ),

      // 图片
      ContentItem(
        title: '设计灵感图',
        type: ContentType.image,
        content: '/path/to/image.png',
        tags: ['设计', '灵感'],
      ),

      // SVG图标
      ContentItem(
        title: 'Logo设计',
        type: ContentType.svg,
        content: '/path/to/logo.svg',
        tags: ['logo', 'svg'],
      ),

      // HTML文档
      ContentItem(
        title: '网页模板',
        type: ContentType.html,
        content: '<html><body><h1>示例网页</h1></body></html>',
        tags: ['网页', '模板'],
      ),

      // PDF文档
      ContentItem(
        title: '用户手册',
        type: ContentType.pdf,
        content: '/path/to/manual.pdf',
        tags: ['手册', 'PDF'],
        isFavorite: true,
      ),

      // 语音文件
      ContentItem(
        title: '会议录音',
        type: ContentType.voice,
        content: '/path/to/meeting.mp3',
        tags: ['会议', '录音'],
      ),

      // 更多示例数据
      ContentItem(
        title: 'Flutter开发指南',
        type: ContentType.markdown,
        content: '# Flutter开发指南\n\n详细的Flutter开发教程...',
        tags: ['Flutter', '开发'],
      ),

      ContentItem(
        title: '产品需求文档',
        type: ContentType.markdownBlocks,
        content: '分块的产品需求文档...',
        tags: ['产品', '需求'],
      ),

      ContentItem(
        title: '每日计划',
        type: ContentType.textCard,
        content: '{"text": "今日目标：完成UI设计优化"}',
        tags: ['text_card', '计划'],
      ),

      ContentItem(
        title: '团队介绍页面',
        type: ContentType.html,
        content: '<html><body><h1>团队介绍</h1></body></html>',
        tags: ['团队', '介绍'],
      ),

      ContentItem(
        title: '产品截图',
        type: ContentType.image,
        content: '/path/to/screenshot.png',
        tags: ['产品', '截图'],
        isFavorite: true,
      ),

      ContentItem(
        title: '技术分享录音',
        type: ContentType.voice,
        content: '/path/to/tech_share.mp3',
        tags: ['技术', '分享'],
      ),
    ];
  }
}

/// 演示应用入口
class DemoApp extends StatelessWidget {
  const DemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '内容库首页演示',
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF667eea),
        ),
      ),
      home: const DemoPremiumContent(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// 运行演示
void main() {
  runApp(const DemoApp());
}
